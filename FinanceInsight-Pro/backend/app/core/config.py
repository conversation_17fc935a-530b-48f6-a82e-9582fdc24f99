"""
应用配置模块
使用Pydantic Settings进行配置管理
"""

from typing import List, Optional, Union
from pydantic import validator, Field
from pydantic_settings import BaseSettings
import os
from pathlib import Path


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "FinanceInsight Pro"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "智能财经新闻分析与A股推荐系统"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 4
    RELOAD: bool = False
    
    # API配置
    API_V1_STR: str = "/api/v1"
    
    # 数据库配置
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    READ_DATABASE_URL: Optional[str] = None
    DB_ECHO: bool = False
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_MAX_CONNECTIONS: int = 20
    
    # MongoDB配置
    MONGODB_URL: str = Field(default="mongodb://localhost:27017/financeinsight", env="MONGODB_URL")
    MONGODB_HOST: str = "localhost"
    MONGODB_PORT: int = 27017
    MONGODB_DB: str = "financeinsight"
    MONGODB_USER: Optional[str] = None
    MONGODB_PASSWORD: Optional[str] = None
    
    # Elasticsearch配置
    ELASTICSEARCH_URL: str = Field(default="http://localhost:9200", env="ELASTICSEARCH_URL")
    ELASTICSEARCH_HOST: str = "localhost"
    ELASTICSEARCH_PORT: int = 9200
    ELASTICSEARCH_INDEX_PREFIX: str = "financeinsight"
    
    # InfluxDB配置
    INFLUXDB_URL: str = Field(default="http://localhost:8086", env="INFLUXDB_URL")
    INFLUXDB_TOKEN: Optional[str] = None
    INFLUXDB_ORG: str = "financeinsight"
    INFLUXDB_BUCKET: str = "stock_data"
    
    # JWT配置
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # 加密配置
    ENCRYPTION_KEY: Optional[str] = None
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    ALLOWED_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    ALLOWED_HEADERS: List[str] = ["*"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    # API限流配置
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_FREE_USER: str = "100/hour"
    RATE_LIMIT_PRO_USER: str = "500/hour"
    RATE_LIMIT_ENTERPRISE_USER: str = "2000/hour"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    LOG_FILE: str = "logs/app.log"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    # 外部API配置
    # 东方财富API
    EASTMONEY_API_KEY: Optional[str] = None
    EASTMONEY_BASE_URL: str = "https://push2.eastmoney.com"
    
    # 同花顺API
    TONGHUASHUN_API_KEY: Optional[str] = None
    TONGHUASHUN_BASE_URL: str = "https://data.10jqka.com.cn"
    
    # 新浪财经API
    SINA_FINANCE_BASE_URL: str = "https://hq.sinajs.cn"
    
    # 腾讯财经API
    TENCENT_FINANCE_BASE_URL: str = "https://qt.gtimg.cn"
    
    # 新闻源API配置
    XINHUA_API_KEY: Optional[str] = None
    XINHUA_BASE_URL: str = "https://api.xinhuanet.com"
    
    CAIXIN_API_KEY: Optional[str] = None
    CAIXIN_BASE_URL: str = "https://api.caixin.com"
    
    YICAI_API_KEY: Optional[str] = None
    YICAI_BASE_URL: str = "https://api.yicai.com"
    
    REUTERS_API_KEY: Optional[str] = None
    REUTERS_BASE_URL: str = "https://api.reuters.com"
    
    BLOOMBERG_API_KEY: Optional[str] = None
    BLOOMBERG_BASE_URL: str = "https://api.bloomberg.com"
    
    # AI模型配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"
    
    BAIDU_AI_API_KEY: Optional[str] = None
    BAIDU_AI_SECRET_KEY: Optional[str] = None
    
    TENCENT_AI_SECRET_ID: Optional[str] = None
    TENCENT_AI_SECRET_KEY: Optional[str] = None
    
    ALIYUN_ACCESS_KEY_ID: Optional[str] = None
    ALIYUN_ACCESS_KEY_SECRET: Optional[str] = None
    
    # 模型文件路径
    MODEL_PATH: str = "models/"
    BERT_MODEL_PATH: str = "models/bert-base-chinese"
    SENTIMENT_MODEL_PATH: str = "models/sentiment_model.pkl"
    RECOMMENDATION_MODEL_PATH: str = "models/recommendation_model.pkl"
    
    # 消息队列配置
    RABBITMQ_URL: str = Field(default="amqp://guest:guest@localhost:5672/", env="RABBITMQ_URL")
    RABBITMQ_HOST: str = "localhost"
    RABBITMQ_PORT: int = 5672
    RABBITMQ_USER: str = "guest"
    RABBITMQ_PASSWORD: str = "guest"
    RABBITMQ_VHOST: str = "/"
    
    # Celery配置
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: List[str] = ["json"]
    CELERY_TIMEZONE: str = "Asia/Shanghai"
    
    # 文件存储配置
    UPLOAD_PATH: str = "uploads/"
    MAX_FILE_SIZE: int = 10485760  # 10MB
    
    # 阿里云OSS
    ALIYUN_OSS_ACCESS_KEY_ID: Optional[str] = None
    ALIYUN_OSS_ACCESS_KEY_SECRET: Optional[str] = None
    ALIYUN_OSS_BUCKET: Optional[str] = None
    ALIYUN_OSS_ENDPOINT: Optional[str] = None
    
    # 腾讯云COS
    TENCENT_COS_SECRET_ID: Optional[str] = None
    TENCENT_COS_SECRET_KEY: Optional[str] = None
    TENCENT_COS_BUCKET: Optional[str] = None
    TENCENT_COS_REGION: Optional[str] = None
    
    # 邮件服务配置
    MAIL_USERNAME: Optional[str] = None
    MAIL_PASSWORD: Optional[str] = None
    MAIL_FROM: Optional[str] = None
    MAIL_PORT: int = 587
    MAIL_SERVER: Optional[str] = None
    MAIL_TLS: bool = True
    MAIL_SSL: bool = False
    
    # 短信服务配置
    SMS_PROVIDER: str = "aliyun"  # aliyun, tencent, twilio
    ALIYUN_SMS_ACCESS_KEY_ID: Optional[str] = None
    ALIYUN_SMS_ACCESS_KEY_SECRET: Optional[str] = None
    ALIYUN_SMS_SIGN_NAME: str = "FinanceInsight"
    ALIYUN_SMS_TEMPLATE_CODE: Optional[str] = None
    
    # 推送服务配置
    PUSH_PROVIDER: str = "firebase"  # firebase, jpush
    FIREBASE_SERVER_KEY: Optional[str] = None
    JPUSH_APP_KEY: Optional[str] = None
    JPUSH_MASTER_SECRET: Optional[str] = None
    
    # 监控配置
    SENTRY_DSN: Optional[str] = None
    PROMETHEUS_ENABLED: bool = True
    PROMETHEUS_PORT: int = 9090
    
    # 健康检查
    HEALTH_CHECK_ENABLED: bool = True
    HEALTH_CHECK_PATH: str = "/health"
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 1小时
    CACHE_MAX_SIZE: int = 1000
    CACHE_ENABLED: bool = True
    
    # 爬虫配置
    CRAWLER_ENABLED: bool = True
    CRAWLER_INTERVAL: int = 300  # 5分钟
    CRAWLER_CONCURRENT_REQUESTS: int = 16
    CRAWLER_DELAY: int = 1
    CRAWLER_USER_AGENT: str = "FinanceInsight-Bot/1.0"
    
    # 数据更新配置
    DATA_UPDATE_INTERVAL: int = 60  # 1分钟
    STOCK_PRICE_UPDATE_INTERVAL: int = 5  # 5秒
    NEWS_UPDATE_INTERVAL: int = 30  # 30秒
    
    # 推荐系统配置
    RECOMMENDATION_REFRESH_INTERVAL: int = 1800  # 30分钟
    RECOMMENDATION_MODEL_RETRAIN_INTERVAL: int = 86400  # 24小时
    RECOMMENDATION_TOP_K: int = 20
    
    # 风控配置
    RISK_CONTROL_ENABLED: bool = True
    MAX_RECOMMENDATION_RISK_LEVEL: int = 5
    RISK_WARNING_THRESHOLD: float = 0.8
    
    # 备份配置
    BACKUP_ENABLED: bool = True
    BACKUP_INTERVAL: int = 86400  # 24小时
    BACKUP_RETENTION_DAYS: int = 30
    BACKUP_PATH: str = "backups/"
    
    # 测试配置
    TEST_DATABASE_URL: Optional[str] = None
    TEST_REDIS_URL: str = "redis://localhost:6379/15"
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """处理CORS origins配置"""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("ALLOWED_METHODS", pre=True)
    def assemble_cors_methods(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """处理CORS methods配置"""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("ALLOWED_HEADERS", pre=True)
    def assemble_cors_headers(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """处理CORS headers配置"""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("MODEL_PATH")
    def validate_model_path(cls, v: str) -> str:
        """验证模型路径"""
        path = Path(v)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
        return str(path)
    
    @validator("UPLOAD_PATH")
    def validate_upload_path(cls, v: str) -> str:
        """验证上传路径"""
        path = Path(v)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
        return str(path)
    
    @validator("BACKUP_PATH")
    def validate_backup_path(cls, v: str) -> str:
        """验证备份路径"""
        path = Path(v)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
        return str(path)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


# 根据环境设置特定配置
if settings.ENVIRONMENT == "development":
    settings.DEBUG = True
    settings.RELOAD = True
    settings.DB_ECHO = True
    settings.LOG_LEVEL = "DEBUG"
elif settings.ENVIRONMENT == "testing":
    settings.DEBUG = True
    settings.DATABASE_URL = settings.TEST_DATABASE_URL or settings.DATABASE_URL
    settings.REDIS_URL = settings.TEST_REDIS_URL
elif settings.ENVIRONMENT == "production":
    settings.DEBUG = False
    settings.RELOAD = False
    settings.DB_ECHO = False
    settings.LOG_LEVEL = "INFO"
